package utils

import (
	"bytes"
	"fmt"
	"runtime"
	"strings"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	// DEBUG 调试级别
	DEBUG LogLevel = iota
	// INFO 信息级别
	INFO
	// WARN 警告级别
	WARN
	// ERROR 错误级别
	ERROR
	// FATAL 致命级别
	FATAL
)

// String 日志级别转字符串
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// GetBlock 获取日志消息块
func GetBlock(msg interface{}) string {
	var buffer bytes.Buffer

	// 添加分隔线
	buffer.WriteString("\n====================  ")

	// 添加时间
	buffer.WriteString(time.Now().Format("2006-01-02 15:04:05"))

	// 添加分隔线
	buffer.WriteString("  ====================\n")

	// 添加消息
	buffer.WriteString(fmt.Sprintf("%v\n", msg))

	// 添加调用栈
	buffer.WriteString(GetCallStack())

	// 添加分隔线
	buffer.WriteString("\n==============================================================\n")

	return buffer.String()
}

// GetCallStack 获取调用栈
func GetCallStack() string {
	var buffer bytes.Buffer

	// 获取调用栈
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(3, pcs[:])
	frames := runtime.CallersFrames(pcs[:n])

	buffer.WriteString("Call Stack:\n")
	for {
		frame, more := frames.Next()
		if !strings.Contains(frame.File, "runtime/") {
			buffer.WriteString(fmt.Sprintf("- %s:%d: %s\n", frame.File, frame.Line, frame.Function))
		}
		if !more {
			break
		}
	}

	return buffer.String()
}

// FormatLogMessage 格式化日志消息
func FormatLogMessage(level LogLevel, format string, args ...interface{}) string {
	// 获取当前时间
	now := time.Now().Format("2006-01-02 15:04:05.000")

	// 获取调用者信息
	_, file, line, ok := runtime.Caller(2)
	caller := "unknown"
	if ok {
		// 截取文件名
		parts := strings.Split(file, "/")
		if len(parts) > 0 {
			file = parts[len(parts)-1]
		}
		caller = fmt.Sprintf("%s:%d", file, line)
	}

	// 格式化消息
	var message string
	if len(args) > 0 {
		message = fmt.Sprintf(format, args...)
	} else {
		message = format
	}

	// 返回格式化后的日志消息
	return fmt.Sprintf("[%s] [%s] [%s] %s", now, level.String(), caller, message)
}

// LogWithLevel 按级别记录日志
func LogWithLevel(level LogLevel, format string, args ...interface{}) {
	message := FormatLogMessage(level, format, args...)
	fmt.Println(message)
}

// Debug 记录调试日志
func Debug(format string, args ...interface{}) {
	LogWithLevel(DEBUG, format, args...)
}

// Info 记录信息日志
func Info(format string, args ...interface{}) {
	LogWithLevel(INFO, format, args...)
}

// Warn 记录警告日志
func Warn(format string, args ...interface{}) {
	LogWithLevel(WARN, format, args...)
}

// Error 记录错误日志
func Error(format string, args ...interface{}) {
	LogWithLevel(ERROR, format, args...)
}

// Fatal 记录致命日志
func Fatal(format string, args ...interface{}) {
	LogWithLevel(FATAL, format, args...)
}

// LogError 记录错误
func LogError(err error) {
	if err == nil {
		return
	}
	Error("Error: %s", err.Error())
}

// LogErrorWithBlock 记录错误块
func LogErrorWithBlock(err error) {
	if err == nil {
		return
	}
	fmt.Println(GetBlock(err))
}
