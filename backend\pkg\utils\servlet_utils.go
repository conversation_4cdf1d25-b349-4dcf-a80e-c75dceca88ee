package utils

import (
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// GetParameter 获取请求参数
func GetParameter(c *gin.Context, name string) string {
	// 先从URL查询参数获取
	value := c.Query(name)
	if value != "" {
		return value
	}

	// 再从表单参数获取
	value = c.PostForm(name)
	if value != "" {
		return value
	}

	// 从路径参数获取
	value = c.Param(name)
	return value
}

// GetParameterWithDefault 获取请求参数，带默认值
func GetParameterWithDefault(c *gin.Context, name, defaultValue string) string {
	value := GetParameter(c, name)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetParameterToInt 获取请求参数并转换为整数
func GetParameterToInt(c *gin.Context, name string) int {
	value := GetParameter(c, name)
	if value == "" {
		return 0
	}
	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0
	}
	return intValue
}

// GetParameterToIntWithDefault 获取请求参数并转换为整数，带默认值
func GetParameterToIntWithDefault(c *gin.Context, name string, defaultValue int) int {
	value := GetParameterToInt(c, name)
	if value == 0 {
		return defaultValue
	}
	return value
}

// GetParameterToBool 获取请求参数并转换为布尔值
func GetParameterToBool(c *gin.Context, name string) bool {
	value := GetParameter(c, name)
	if value == "" {
		return false
	}
	boolValue, err := strconv.ParseBool(value)
	if err != nil {
		return false
	}
	return boolValue
}

// GetParameterToBoolWithDefault 获取请求参数并转换为布尔值，带默认值
func GetParameterToBoolWithDefault(c *gin.Context, name string, defaultValue bool) bool {
	value := GetParameter(c, name)
	if value == "" {
		return defaultValue
	}
	boolValue, err := strconv.ParseBool(value)
	if err != nil {
		return defaultValue
	}
	return boolValue
}

// GetParams 获取所有请求参数
func GetParams(c *gin.Context) map[string][]string {
	params := make(map[string][]string)

	// 获取URL查询参数
	for k, v := range c.Request.URL.Query() {
		params[k] = v
	}

	// 获取表单参数
	if c.Request.Method == "POST" || c.Request.Method == "PUT" {
		if c.Request.Form == nil {
			_ = c.Request.ParseForm()
		}
		for k, v := range c.Request.PostForm {
			if _, ok := params[k]; !ok {
				params[k] = v
			}
		}
	}

	return params
}

// GetParamMap 获取所有请求参数(单值)
func GetParamMap(c *gin.Context) map[string]string {
	paramMap := make(map[string]string)

	params := GetParams(c)
	for k, v := range params {
		if len(v) > 0 {
			paramMap[k] = v[0]
		} else {
			paramMap[k] = ""
		}
	}

	return paramMap
}

// RenderString 向响应写入字符串
func RenderString(c *gin.Context, content string) {
	c.Header("Content-Type", "text/plain;charset=UTF-8")
	c.String(http.StatusOK, content)
}

// RenderJSON 向响应写入JSON
func RenderJSON(c *gin.Context, obj interface{}) {
	c.Header("Content-Type", "application/json;charset=UTF-8")
	c.JSON(http.StatusOK, obj)
}

// IsAjaxRequest 判断是否是Ajax请求
func IsAjaxRequest(c *gin.Context) bool {
	xRequestedWith := c.GetHeader("X-Requested-With")
	return strings.EqualFold(xRequestedWith, "XMLHttpRequest")
}

// UrlEncode URL编码
func UrlEncode(str string) string {
	return url.QueryEscape(str)
}

// UrlDecode URL解码
func UrlDecode(str string) (string, error) {
	return url.QueryUnescape(str)
}

// GetRequestIP 获取请求IP
func GetRequestIP(c *gin.Context) string {
	xForwardedFor := c.GetHeader("X-Forwarded-For")
	if xForwardedFor != "" {
		// 可能有多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 检查代理IP
	xRealIP := c.GetHeader("X-Real-IP")
	if xRealIP != "" {
		return xRealIP
	}

	// 直接获取远程地址
	return c.ClientIP()
}

// GetUserAgent 获取用户代理
func GetUserAgent(c *gin.Context) string {
	return c.GetHeader("User-Agent")
}

// GetRequestDomain 获取请求域名
func GetRequestDomain(c *gin.Context) string {
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	return scheme + "://" + c.Request.Host
}
