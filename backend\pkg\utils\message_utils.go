package utils

import (
	"fmt"
	"strings"
	"sync"
)

// 消息资源
var messageResources = make(map[string]map[string]string)
var messageResourcesMutex sync.RWMutex

// 默认语言
var defaultLocale = "zh_CN"

// SetMessageResource 设置消息资源
func SetMessageResource(locale string, messages map[string]string) {
	messageResourcesMutex.Lock()
	defer messageResourcesMutex.Unlock()
	messageResources[locale] = messages
}

// SetDefaultLocale 设置默认语言
func SetDefaultLocale(locale string) {
	defaultLocale = locale
}

// GetDefaultLocale 获取默认语言
func GetDefaultLocale() string {
	return defaultLocale
}

// Message 获取消息，使用默认语言
func Message(code string, args ...interface{}) string {
	return MessageWithLocale(defaultLocale, code, args...)
}

// MessageWithLocale 获取指定语言的消息
func MessageWithLocale(locale, code string, args ...interface{}) string {
	messageResourcesMutex.RLock()
	defer messageResourcesMutex.RUnlock()

	// 获取指定语言的消息资源
	messages, ok := messageResources[locale]
	if !ok {
		// 如果指定语言的消息资源不存在，尝试使用默认语言
		messages, ok = messageResources[defaultLocale]
		if !ok {
			// 如果默认语言的消息资源也不存在，直接返回消息编码
			return code
		}
	}

	// 获取消息模板
	message, ok := messages[code]
	if !ok {
		// 如果消息编码不存在，直接返回消息编码
		return code
	}

	// 如果没有参数，直接返回消息模板
	if len(args) == 0 {
		return message
	}

	// 处理带占位符的消息模板
	if strings.Contains(message, "%") {
		return fmt.Sprintf(message, args...)
	}

	// 处理带{n}占位符的消息模板
	for i, arg := range args {
		placeholder := fmt.Sprintf("{%d}", i)
		message = strings.Replace(message, placeholder, fmt.Sprintf("%v", arg), -1)
	}

	return message
}

// AddMessageResource 添加单个消息资源
func AddMessageResource(locale, code, message string) {
	messageResourcesMutex.Lock()
	defer messageResourcesMutex.Unlock()

	// 获取指定语言的消息资源
	messages, ok := messageResources[locale]
	if !ok {
		// 如果指定语言的消息资源不存在，创建新的
		messages = make(map[string]string)
		messageResources[locale] = messages
	}

	// 添加消息
	messages[code] = message
}

// RemoveMessageResource 移除单个消息资源
func RemoveMessageResource(locale, code string) {
	messageResourcesMutex.Lock()
	defer messageResourcesMutex.Unlock()

	// 获取指定语言的消息资源
	messages, ok := messageResources[locale]
	if !ok {
		return
	}

	// 移除消息
	delete(messages, code)
}

// ClearMessageResources 清空消息资源
func ClearMessageResources() {
	messageResourcesMutex.Lock()
	defer messageResourcesMutex.Unlock()
	messageResources = make(map[string]map[string]string)
}

// LoadMessageResources 加载消息资源
func LoadMessageResources(resources map[string]map[string]string) {
	messageResourcesMutex.Lock()
	defer messageResourcesMutex.Unlock()
	for locale, messages := range resources {
		if _, ok := messageResources[locale]; !ok {
			messageResources[locale] = make(map[string]string)
		}
		for code, message := range messages {
			messageResources[locale][code] = message
		}
	}
}

// 常用响应消息
const (
	// 成功消息
	MsgSuccess = "操作成功"
	// 失败消息
	MsgError = "操作失败"
	// 未授权消息
	MsgUnauthorized = "未授权，请重新登录"
	// 禁止访问消息
	MsgForbidden = "没有权限，禁止访问"
	// 参数错误消息
	MsgParamError = "参数错误"
	// 系统错误消息
	MsgSystemError = "系统错误，请联系管理员"
)
